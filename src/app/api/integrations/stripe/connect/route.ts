import { auth } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Authenticate the user
    const { userId, orgId } = await auth();

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    if (!orgId) {
      return NextResponse.json(
        { error: 'Organization required' },
        { status: 400 }
      );
    }

    // Determine environment based on Vercel environment variables
    const vercelEnv =
      process.env.VERCEL_ENV || process.env.NODE_ENV || 'production';

    // Map Vercel environments to our environment types
    let environment: 'development' | 'production';
    if (vercelEnv === 'development' || vercelEnv === 'preview') {
      environment = 'development';
    } else if (vercelEnv === 'production') {
      environment = 'production';
    } else {
      return NextResponse.json(
        { error: 'Invalid environment' },
        { status: 400 }
      );
    }

    // Validate required environment variables
    const clientId = process.env.STRIPE_CLIENT_ID;
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || process.env.VERCEL_URL;

    if (!clientId) {
      console.error('STRIPE_CLIENT_ID environment variable is not set');
      return NextResponse.json(
        { error: 'Stripe configuration missing' },
        { status: 500 }
      );
    }

    if (!baseUrl) {
      console.error(
        'NEXT_PUBLIC_BASE_URL or VERCEL_URL environment variable is not set'
      );
      return NextResponse.json(
        { error: 'Base URL configuration missing' },
        { status: 500 }
      );
    }

    // Construct the callback URL
    const protocol = baseUrl.startsWith('http') ? '' : 'https://';
    const callbackUrl = `${protocol}${baseUrl}/api/integrations/stripe/callback`;

    // Create state parameter to prevent CSRF attacks and pass org info
    const state = Buffer.from(
      JSON.stringify({
        orgId,
        userId,
        environment,
        timestamp: Date.now(),
      })
    ).toString('base64url');

    // Construct Stripe OAuth URL
    const stripeOAuthUrl = new URL(
      'https://connect.stripe.com/oauth/authorize'
    );
    stripeOAuthUrl.searchParams.set('response_type', 'code');
    stripeOAuthUrl.searchParams.set('client_id', clientId);
    stripeOAuthUrl.searchParams.set('scope', 'read_write');
    stripeOAuthUrl.searchParams.set('redirect_uri', callbackUrl);
    stripeOAuthUrl.searchParams.set('state', state);

    // Add suggested capabilities for better UX
    stripeOAuthUrl.searchParams.set(
      'suggested_capabilities[]',
      'card_payments'
    );
    stripeOAuthUrl.searchParams.set('suggested_capabilities[]', 'transfers');

    return NextResponse.json({
      url: stripeOAuthUrl.toString(),
      state,
    });
  } catch (error) {
    console.error('Error creating Stripe OAuth URL:', error);
    return NextResponse.json(
      { error: 'Failed to create OAuth URL' },
      { status: 500 }
    );
  }
}
