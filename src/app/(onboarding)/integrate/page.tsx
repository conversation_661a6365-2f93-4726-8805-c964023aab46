interface IntegratePageProps {
  searchParams: {
    success?: string;
    error?: string;
    error_description?: string;
    provider?: string;
  };
}

export default function IntegratePage({ searchParams }: IntegratePageProps) {
  const success = searchParams.success === 'true';
  const error = searchParams.error;
  const errorDescription = searchParams.error_description;
  const provider = searchParams.provider ?? 'Stripe';

  return (
    <div className='flex justify-center py-24 flex flex-col items-center max-w-2xl mx-auto'>
      <h1 className='text-2xl font-semibold text-gray-900 mb-4'>
        Choose your payment processor
      </h1>

      {success && (
        <div className='mb-6 p-4 bg-green-50 border border-green-200 rounded-md'>
          <p className='text-sm font-medium text-green-800'>
            Successfully connected {provider}!
          </p>
        </div>
      )}

      {error && (
        <div className='mb-6 p-4 bg-red-50 border border-red-200 rounded-md'>
          <p className='text-sm font-medium text-red-800'>
            Integration failed: {error}
          </p>
          {errorDescription && (
            <p className='text-sm text-red-700 mt-1'>{errorDescription}</p>
          )}
        </div>
      )}

      <div className='bg-white border border-gray-200 rounded-lg p-6 shadow-sm w-full max-w-md'>
        <div className='flex items-center mb-4'>
          <div
            className='w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mr-4'
            aria-hidden
          >
            {/* icon */}
            <svg
              className='w-6 h-6 text-white'
              fill='currentColor'
              viewBox='0 0 24 24'
            >
              <path d='M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.975 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z' />
            </svg>
          </div>
          <div>
            <h3 className='text-lg font-medium text-gray-900'>Stripe</h3>
            <p className='text-sm text-gray-500'>Accept payments online</p>
          </div>
        </div>

        {/* Plain link/button to your API route that redirects */}
        <a
          href='/api/integrations/stripe/connect'
          className='w-full block text-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200'
        >
          Connect with Stripe
        </a>

        <p className='text-xs text-gray-500 mt-3 text-center'>
          By connecting, you agree to Stripe&apos;s terms of service and privacy
          policy.
        </p>
      </div>
    </div>
  );
}
